#!/usr/bin/env python3
"""
Test script to verify location updates are working properly
"""
import os
import sys
import django
import time
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'public_transport.settings')
django.setup()

from drivers.models import Driver
from tracking.models import Location
from django.utils import timezone

def test_location_updates():
    """Test location update functionality"""
    print("🧪 Testing Location Updates...")
    
    # Get a test driver
    try:
        driver = Driver.objects.filter(is_on_duty=True).first()
        if not driver:
            print("❌ No drivers on duty found. Please start duty for at least one driver.")
            return False
            
        print(f"✅ Found test driver: {driver.username}")
        
        # Check current location
        print(f"📍 Current location: {driver.current_latitude}, {driver.current_longitude}")
        print(f"🕒 Last update: {driver.last_location_update}")
        
        # Check recent location history
        recent_locations = Location.objects.filter(
            driver=driver
        ).order_by('-timestamp')[:5]
        
        print(f"📊 Recent location history ({len(recent_locations)} records):")
        for i, loc in enumerate(recent_locations, 1):
            print(f"  {i}. {loc.latitude}, {loc.longitude} at {loc.timestamp}")
            
        # Check if locations are being updated recently
        if driver.last_location_update:
            time_diff = timezone.now() - driver.last_location_update
            if time_diff.total_seconds() < 60:  # Updated within last minute
                print("✅ Location updates are recent (within last minute)")
                return True
            else:
                print(f"⚠️  Last location update was {time_diff.total_seconds():.0f} seconds ago")
                return False
        else:
            print("❌ No location updates found")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

def monitor_location_updates(duration=60):
    """Monitor location updates for a specified duration"""
    print(f"🔍 Monitoring location updates for {duration} seconds...")
    
    driver = Driver.objects.filter(is_on_duty=True).first()
    if not driver:
        print("❌ No drivers on duty found.")
        return
        
    print(f"👀 Monitoring driver: {driver.username}")
    
    start_time = time.time()
    last_update = driver.last_location_update
    update_count = 0
    
    while time.time() - start_time < duration:
        driver.refresh_from_db()
        
        if driver.last_location_update and driver.last_location_update != last_update:
            update_count += 1
            print(f"📍 Update #{update_count}: {driver.current_latitude}, {driver.current_longitude} at {driver.last_location_update}")
            last_update = driver.last_location_update
            
        time.sleep(5)  # Check every 5 seconds
        
    print(f"📊 Monitoring complete. Received {update_count} location updates in {duration} seconds.")
    if update_count > 0:
        print("✅ Location updates are working!")
    else:
        print("❌ No location updates received during monitoring period.")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
        monitor_location_updates(duration)
    else:
        test_location_updates()
