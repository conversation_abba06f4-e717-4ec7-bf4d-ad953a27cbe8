{% extends "base.html" %}
{% load static %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">مرحبًا, {{ driver.username }}</h4>
                <div class="mt-2">
                </div>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <strong>القرية:</strong> {{ driver.village }} |
                        <strong>رقم الهاتف:</strong> {{ driver.phone_number }}
                    </div>
                    <div>
                        <button id="toggleDutyBtn" class="btn {% if driver.is_on_duty %}btn-danger{% else %}btn-success{% endif %}">
                            {% if driver.is_on_duty %}
                                إيقاف الدوام
                            {% else %}
                                بدء الدوام
                            {% endif %}
                        </button>
                        <button id="testLocationBtn" class="btn btn-info btn-sm ms-2" onclick="testLocationUpdate()">
                            🧪 اختبار الموقع
                        </button>
                    </div>
                </div>

                <div class="alert alert-info" id="statusAlert">
                    {% if driver.is_on_duty %}
                        حالة الدوام: <strong class="text-success">جاري العمل</strong>
                        <p class="mb-0">🔄 يتم تتبع موقعك في الوقت الفعلي (كل 10 ثوانِ)</p>
                    {% else %}
                        حالة الدوام: <strong class="text-danger">متوقف</strong>
                        <p class="mb-0">لن يتم تتبع موقعك حتى تقوم بتشغيل الدوام</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">خريطة الموقع</h4>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h5>المسارات المسموحة: {{ driver.permitted_routes }}</h5>
                </div>
                <div id="map" style="height: 500px; width: 100%;" data-permitted-routes="{{ driver.permitted_routes }}"></div>
                

                {% if selected_village %}
                <div class="mt-3">
                    <h5>سائقي قرية {{ selected_village }}</h5>
                    <ul id="drivers-list" class="list-group">
                        {% for driver in drivers %}
                        <li class="list-group-item">{{ driver.username }} - {{ driver.status }}</li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register("{% static 'sw.js' %}")
    .then(registration => {
      console.log('ServiceWorker registration successful');
    })
    .catch(err => {
      console.error('ServiceWorker registration failed: ', err);
    });
}
</script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<style>
    #map {
        height: 500px;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0,0,0,0.2);
        z-index: 1;
    }
    .leaflet-control-attribution {
        font-size: 11px;
    }
    .driver-location {
        background-color: #007bff;
        border-radius: 50%;
        border: 2px solid white;
    }
</style>
<script>
    let map;
    let userMarker;
    let positionInterval;

    function checkPrerequisites() {
        // Check if Leaflet loaded
        if (typeof L === 'undefined') {
            console.error('LeafletJS not loaded - check network tab');
            alert('Failed to load map library. Please check console for details.');
            return false;
        }

        // Check if map container exists
        const mapEl = document.getElementById('map');
        if (!mapEl) {
            console.error('Map container element not found');
            return false;
        }
        
        // Verify container visibility and dimensions
        const style = window.getComputedStyle(mapEl);
        if (style.display === 'none') {
            console.error('Map container is hidden');
            mapEl.style.display = 'block';
        }
        
        mapEl.style.height = mapEl.style.height || '500px'; 
        mapEl.style.width = mapEl.style.width || '100%';
        return true;
    }

    function initializeMap() {
        console.log('Initializing map...');
        
        if (!checkPrerequisites()) {
            console.error('Missing prerequisites for map initialization');
            return;
        }

        try {
            console.log('Creating Leaflet map instance');
            if (typeof L === 'undefined') {
                throw new Error('Leaflet library not loaded - check network requests');
            }

            const mapEl = document.getElementById('map');
            const permittedRoutes = mapEl.dataset.permittedRoutes;
            
            // Only create map if it doesn't exist
            if (!map) {
                map = L.map('map', {
                    zoomControl: true,
                    preferCanvas: true
                }).setView([31.9, 35.2], 11);

                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    maxZoom: 18,
                    minZoom: 10
                }).addTo(map);

                L.control.scale({position: 'bottomleft'}).addTo(map);

                // Add permitted routes if available
                if (permittedRoutes) {
                    const routes = permittedRoutes.split(',').map(r => r.trim());
                    for (const route of routes) {
                        const [start, end] = route.split('-').map(loc => loc.trim());
                        // You should implement getRouteCoords or integrate with actual API
                        // Example placeholder:
                        const coords = [
                            [31.9, 35.2],
                            [32.0, 35.3]
                        ];
                        L.polyline(coords, { color: 'blue' }).addTo(map)
                            .bindPopup(`<b>${route}</b>`);
                    }
                }

                console.log('Map initialized successfully');

                // Use driver's saved location if available, otherwise get current location
                {% if driver.current_latitude and driver.current_longitude %}
                    console.log('Using saved driver location');
                    const savedLat = {{ driver.current_latitude }};
                    const savedLng = {{ driver.current_longitude }};
                    updateLocation(savedLat, savedLng);
                {% else %}
                    console.log('No saved location, getting current location');
                    // Get initial location to show driver's position immediately
                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            function(position) {
                                const latitude = position.coords.latitude;
                                const longitude = position.coords.longitude;
                                console.log('Got initial location:', latitude, longitude);
                                updateLocation(latitude, longitude);
                            },
                            function(error) {
                                console.warn('Could not get initial location:', error.message);
                                // Set default location if geolocation fails
                                updateLocation(31.9, 35.2);
                            },
                            {
                                enableHighAccuracy: true,
                                timeout: 10000,
                                maximumAge: 60000
                            }
                        );
                    } else {
                        console.warn('Geolocation not supported, using default location');
                        updateLocation(31.9, 35.2);
                    }
                {% endif %}
            }
        } catch (error) {
            console.error('Error initializing map:', error);
            alert('Error initializing map: ' + error.message);
        }
    }

    let driverSocket;
    
    function initializeLocationTracking() {
        console.log('Initializing location tracking...');

        // Set up WebSocket connection
        const wsScheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
        driverSocket = new WebSocket(
            `${wsScheme}://${window.location.host}/ws/drivers/location/`
        );

        driverSocket.onopen = function(e) {
            console.log('WebSocket connection established for location tracking');
        };

        driverSocket.onclose = function(e) {
            console.log('WebSocket disconnected, attempting to reconnect...');
            if (window.isOnDuty) {
                setTimeout(initializeLocationTracking, 5000);
            }
        };

        driverSocket.onerror = function(err) {
            console.error('WebSocket error:', err);
        };

        // Clear any existing interval
        if (positionInterval) {
            clearInterval(positionInterval);
        }

        // Start continuous location tracking every 10 seconds
        positionInterval = setInterval(() => {
            if (!window.isOnDuty) {
                console.log('Driver not on duty, stopping location updates');
                return;
            }

            navigator.geolocation.getCurrentPosition(pos => {
                const latitude = pos.coords.latitude;
                const longitude = pos.coords.longitude;

                console.log(`Location update: ${latitude}, ${longitude}`);

                // Update location on the map
                updateLocation(latitude, longitude);

                // Send location via WebSocket first
                if (driverSocket && driverSocket.readyState === WebSocket.OPEN) {
                    driverSocket.send(JSON.stringify({
                        'latitude': latitude,
                        'longitude': longitude
                    }));
                    console.log('Location sent via WebSocket');
                    updateLocationStatus('✅ موقع محدث (WebSocket)');
                } else {
                    console.log('WebSocket not ready, using HTTP fallback');
                    // Fallback to HTTP if WebSocket is not available
                    fetch('/drivers/update-location/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: `latitude=${latitude}&longitude=${longitude}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            console.log('Location updated via HTTP');
                            updateLocationStatus('✅ موقع محدث (HTTP)');
                        } else {
                            console.error('HTTP location update failed:', data);
                            updateLocationStatus('❌ فشل تحديث الموقع');
                        }
                    })
                    .catch(error => {
                        console.error('HTTP location update error:', error);
                    });
                }
            },
            err => {
                console.error('Geolocation error:', err);
                // Handle different types of geolocation errors
                if (err.code === err.PERMISSION_DENIED) {
                    alert('يجب السماح بالوصول إلى الموقع الجغرافي لتتبع الموقع');
                    // Stop tracking if permission denied
                    stopLocationTracking();
                    window.isOnDuty = false;
                } else if (err.code === err.POSITION_UNAVAILABLE) {
                    console.warn('Location unavailable, will retry...');
                } else if (err.code === err.TIMEOUT) {
                    console.warn('Location timeout, will retry...');
                }
            },
            {
                enableHighAccuracy: true,
                maximumAge: 30000, // Accept cached position up to 30 seconds old
                timeout: 15000     // Wait up to 15 seconds for location
            });
        }, 10000); // Update every 10 seconds

        console.log('Location tracking started with 10-second intervals');
    }

    function updateLocation(latitude, longitude) {
        // Handle both position object and separate parameters
        if (typeof latitude === 'object' && latitude.coords) {
            longitude = latitude.coords.longitude;
            latitude = latitude.coords.latitude;
        }

        console.log(`Updating map marker to: ${latitude}, ${longitude}`);

        // Update map marker
        if (userMarker) {
            userMarker.setLatLng([latitude, longitude]);
            console.log('Updated existing marker position');
        } else {
            userMarker = L.marker([latitude, longitude], {
                icon: L.icon({
                    iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png',
                    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png',
                    iconSize: [25, 41],
                    iconAnchor: [12, 41],
                    popupAnchor: [1, -34],
                    shadowSize: [41, 41]
                })
            }).addTo(map)
                .bindPopup("📍 موقعك الحالي")
                .openPopup();
            console.log('Created new marker');
        }

        // Center map on user location if it's the first time or if map is not initialized properly
        if (!map.hasUserLocation || map.getZoom() < 10) {
            map.setView([latitude, longitude], 15);
            map.hasUserLocation = true;
            console.log('Centered map on user location');
        }
    }

    function updateLocationStatus(message) {
        const statusAlert = document.getElementById('statusAlert');
        if (statusAlert && window.isOnDuty) {
            const currentTime = new Date().toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            statusAlert.innerHTML = `حالة الدوام: <strong class="text-success">جاري العمل</strong>
                <p class="mb-0">🔄 يتم تتبع موقعك في الوقت الفعلي (كل 10 ثوانِ)</p>
                <small class="text-muted">${message} - ${currentTime}</small>`;
        }
    }

    function stopLocationTracking() {
        console.log('Stopping location tracking...');

        // Clear the location tracking interval
        if (positionInterval) {
            clearInterval(positionInterval);
            positionInterval = null;
        }

        // Close WebSocket connection
        if (driverSocket) {
            driverSocket.close();
            driverSocket = null;
        }

        // Remove user marker from map (optional - you might want to keep it)
        // if (userMarker && map) {
        //     map.removeLayer(userMarker);
        //     userMarker = null;
        // }
    }



    document.addEventListener('DOMContentLoaded', function() {
        initializeMap();

        // Initialize global duty status
        window.isOnDuty = {{ driver.is_on_duty|yesno:"true,false" }};

        // If driver is already on duty when page loads, start location tracking
        if (window.isOnDuty) {
            console.log('Driver is already on duty, starting location tracking...');
            initializeLocationTracking();
        }

        // Update status message to reflect real-time updates
        const statusAlert = document.getElementById('statusAlert');
        if (statusAlert && statusAlert.innerHTML.includes('10 ثوانِ')) {
            statusAlert.innerHTML = statusAlert.innerHTML.replace(
                'سيتم تتبع موقعك تلقائيًا كل 10 ثوانِ',
                'سيتم تحديث موقعك في الوقت الفعلي'
            );
        }
        
        document.getElementById('toggleDutyBtn').addEventListener('click', function() {
            const button = document.getElementById('toggleDutyBtn');
            const isStartingDuty = button.textContent.includes('بدء الدوام');
            
            if (isStartingDuty) {
                navigator.geolocation.getCurrentPosition(pos => {
                    const { latitude, longitude } = pos.coords;
                    fetch('/drivers/toggle-duty/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': '{{ csrf_token }}'
                        },
                        body: `latitude=${latitude}&longitude=${longitude}`
                    })
                    .then(response => response.json())
                    .then(handleToggleResponse)
                    .catch(err => {
                        console.error('Error:', err);
                        alert('حدث خطأ أثناء محاولة بدء الدوام');
                    });
                }, err => {
                    console.error('Geolocation error:', err);
                    alert('يجب السماح بالوصول إلى الموقع الجغرافي لبدء الدوام');
                }, {
                    enableHighAccuracy: true,
                    timeout: 30000
                });
            } else {
                fetch('/drivers/toggle-duty/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': '{{ csrf_token }}'
                    }
                })
                .then(response => response.json())
                .then(handleToggleResponse)
                .catch(err => {
                    console.error('Error:', err);
                    alert('حدث خطأ أثناء محاولة إيقاف الدوام');
                });
            }
        });
    });

    function handleToggleResponse(data) {
        const statusAlert = document.getElementById('statusAlert');
        const button = document.getElementById('toggleDutyBtn');
        window.isOnDuty = data.is_on_duty;

        if (data.is_on_duty) {
            button.textContent = 'إيقاف الدوام';
            

            button.classList.remove('btn-success');
            button.classList.add('btn-danger');
            statusAlert.innerHTML = `حالة الدوام: <strong class="text-success">جاري العمل</strong>
                <p class="mb-0">🔄 يتم تتبع موقعك في الوقت الفعلي (كل 10 ثوانِ)</p>`;
            initializeLocationTracking();
        } else {
            button.textContent = 'بدء الدوام';
            button.classList.remove('btn-danger');
            button.classList.add('btn-success');
            statusAlert.innerHTML = `حالة الدوام: <strong class="text-danger">متوقف</strong>
                <p class="mb-0">لن يتم تتبع موقعك حتى تقوم بتشغيل الدوام</p>`;
            stopLocationTracking();
        }
    }

    // Test function to manually trigger location update
    function testLocationUpdate() {
        console.log('Testing location update...');
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const latitude = position.coords.latitude;
                    const longitude = position.coords.longitude;
                    console.log('Test location:', latitude, longitude);
                    updateLocation(latitude, longitude);
                    alert(`موقع اختبار: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`);
                },
                function(error) {
                    console.error('Test location error:', error);
                    alert('خطأ في الحصول على الموقع: ' + error.message);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000
                }
            );
        } else {
            alert('المتصفح لا يدعم تحديد الموقع الجغرافي');
        }
    }
</script>
{% endblock %}
